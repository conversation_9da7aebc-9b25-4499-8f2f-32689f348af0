# Generated by Django 4.2.23 on 2025-08-29 02:53

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('marketplace', '__first__'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='DailyMetrics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(unique=True)),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('new_users', models.PositiveIntegerField(default=0)),
                ('active_users', models.PositiveIntegerField(default=0)),
                ('new_sellers', models.PositiveIntegerField(default=0)),
                ('new_freelancers', models.PositiveIntegerField(default=0)),
                ('new_products', models.PositiveIntegerField(default=0)),
                ('new_services', models.PositiveIntegerField(default=0)),
                ('total_page_views', models.PositiveIntegerField(default=0)),
                ('unique_visitors', models.PositiveIntegerField(default=0)),
                ('new_support_tickets', models.PositiveIntegerField(default=0)),
                ('resolved_support_tickets', models.PositiveIntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='UserAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('total_spent', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('average_order_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('login_count', models.PositiveIntegerField(default=0)),
                ('last_activity', models.DateTimeField(blank=True, null=True)),
                ('reviews_given', models.PositiveIntegerField(default=0)),
                ('reviews_received', models.PositiveIntegerField(default=0)),
                ('average_rating_received', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='StoreAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('total_products_sold', models.PositiveIntegerField(default=0)),
                ('total_customers', models.PositiveIntegerField(default=0)),
                ('returning_customers', models.PositiveIntegerField(default=0)),
                ('total_products', models.PositiveIntegerField(default=0)),
                ('active_products', models.PositiveIntegerField(default=0)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0)),
                ('sales_this_month', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('sales_last_month', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('orders_this_month', models.PositiveIntegerField(default=0)),
                ('orders_last_month', models.PositiveIntegerField(default=0)),
                ('store_views', models.PositiveIntegerField(default=0)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='marketplace.store')),
            ],
        ),
        migrations.CreateModel(
            name='ServiceAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_bookings', models.PositiveIntegerField(default=0)),
                ('completed_services', models.PositiveIntegerField(default=0)),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('unique_views', models.PositiveIntegerField(default=0)),
                ('average_completion_time', models.DurationField(blank=True, null=True)),
                ('on_time_delivery_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('service', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='marketplace.service')),
            ],
        ),
        migrations.CreateModel(
            name='ProductAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('total_orders', models.PositiveIntegerField(default=0)),
                ('units_sold', models.PositiveIntegerField(default=0)),
                ('total_views', models.PositiveIntegerField(default=0)),
                ('unique_views', models.PositiveIntegerField(default=0)),
                ('conversion_rate', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=5)),
                ('average_rating', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=3)),
                ('total_reviews', models.PositiveIntegerField(default=0)),
                ('sales_this_month', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('views_this_month', models.PositiveIntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('product', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='analytics', to='marketplace.product')),
            ],
        ),
        migrations.CreateModel(
            name='SearchAnalytics',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.CharField(max_length=200)),
                ('results_count', models.PositiveIntegerField(default=0)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('clicked_product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.product')),
                ('clicked_service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.service')),
                ('clicked_store', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.store')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['query'], name='analytics_s_query_0468ae_idx'), models.Index(fields=['created_at'], name='analytics_s_created_7c236b_idx'), models.Index(fields=['user'], name='analytics_s_user_id_3e3c83_idx')],
            },
        ),
        migrations.CreateModel(
            name='PageView',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('page_type', models.CharField(choices=[('product', 'Product'), ('service', 'Service'), ('store', 'Store'), ('category', 'Category'), ('search', 'Search'), ('home', 'Home'), ('other', 'Other')], max_length=20)),
                ('page_url', models.URLField()),
                ('session_id', models.CharField(blank=True, max_length=100)),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True)),
                ('user_agent', models.TextField(blank=True)),
                ('referrer', models.URLField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.category')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.product')),
                ('service', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.service')),
                ('store', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='marketplace.store')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['page_type', 'created_at'], name='analytics_p_page_ty_90793b_idx'), models.Index(fields=['user', 'created_at'], name='analytics_p_user_id_51b4ce_idx'), models.Index(fields=['session_id'], name='analytics_p_session_a580f2_idx')],
            },
        ),
    ]
