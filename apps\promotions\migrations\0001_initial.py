# Generated by Django 4.2.23 on 2025-08-29 02:53

from decimal import Decimal
from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('marketplace', '__first__'),
        ('orders', '__first__'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AffiliateProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_rate', models.DecimalField(decimal_places=2, help_text='Commission percentage', max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('referral_code', models.CharField(max_length=50, unique=True)),
                ('total_referrals', models.PositiveIntegerField(default=0)),
                ('total_sales', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=12)),
                ('total_commission', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('affiliate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='affiliate_programs', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Coupon',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True)),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('discount_type', models.CharField(choices=[('percentage', 'Percentage'), ('fixed_amount', 'Fixed Amount'), ('free_shipping', 'Free Shipping')], max_length=20)),
                ('discount_value', models.DecimalField(decimal_places=2, max_digits=10)),
                ('coupon_type', models.CharField(choices=[('general', 'General'), ('first_order', 'First Order'), ('store_specific', 'Store Specific'), ('product_specific', 'Product Specific'), ('user_specific', 'User Specific')], default='general', max_length=20)),
                ('usage_limit', models.PositiveIntegerField(blank=True, help_text='Total usage limit', null=True)),
                ('usage_limit_per_user', models.PositiveIntegerField(blank=True, help_text='Usage limit per user', null=True)),
                ('used_count', models.PositiveIntegerField(default=0)),
                ('minimum_order_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('valid_from', models.DateTimeField()),
                ('valid_until', models.DateTimeField()),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('applicable_categories', models.ManyToManyField(blank=True, to='marketplace.category')),
                ('applicable_products', models.ManyToManyField(blank=True, to='marketplace.product')),
                ('applicable_stores', models.ManyToManyField(blank=True, to='marketplace.store')),
                ('applicable_users', models.ManyToManyField(blank=True, to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_coupons', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FlashSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('description', models.TextField(blank=True)),
                ('discount_percentage', models.DecimalField(decimal_places=2, max_digits=5, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField()),
                ('max_quantity_per_user', models.PositiveIntegerField(blank=True, null=True)),
                ('total_quantity_limit', models.PositiveIntegerField(blank=True, null=True)),
                ('sold_quantity', models.PositiveIntegerField(default=0)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
                ('products', models.ManyToManyField(related_name='flash_sales', to='marketplace.product')),
            ],
            options={
                'ordering': ['-start_time'],
            },
        ),
        migrations.CreateModel(
            name='AffiliateReferral',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('commission_amount', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_commission_paid', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('commission_paid_at', models.DateTimeField(blank=True, null=True)),
                ('affiliate_program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='referrals', to='promotions.affiliateprogram')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('referred_user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='referrals', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CouponUsage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('discount_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('used_at', models.DateTimeField(auto_now_add=True)),
                ('coupon', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='usages', to='promotions.coupon')),
                ('order', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='orders.order')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['coupon', 'user'], name='promotions__coupon__6ff8ff_idx'), models.Index(fields=['used_at'], name='promotions__used_at_625b98_idx')],
                'unique_together': {('coupon', 'order')},
            },
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['code'], name='promotions__code_acae7b_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['valid_from', 'valid_until'], name='promotions__valid_f_d8a470_idx'),
        ),
        migrations.AddIndex(
            model_name='coupon',
            index=models.Index(fields=['is_active'], name='promotions__is_acti_8daf39_idx'),
        ),
    ]
