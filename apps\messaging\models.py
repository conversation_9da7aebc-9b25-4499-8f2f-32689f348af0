from django.db import models
from django.conf import settings
from django.utils import timezone


class Conversation(models.Model):
    """A conversation between users"""
    participants = models.ManyToManyField(settings.AUTH_USER_MODEL, related_name='conversations')
    subject = models.CharField(max_length=200, blank=True)

    # Conversation metadata
    is_support_ticket = models.BooleanField(default=False)
    is_archived = models.BooleanField(default=False)

    # Related objects (optional)
    related_order = models.ForeignKey('orders.Order', on_delete=models.SET_NULL, null=True, blank=True)
    related_product = models.ForeignKey('marketplace.Product', on_delete=models.SET_NULL, null=True, blank=True)
    related_service = models.ForeignKey('marketplace.Service', on_delete=models.SET_NULL, null=True, blank=True)
    related_store = models.ForeignKey('marketplace.Store', on_delete=models.SET_NULL, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']

    def __str__(self):
        participants_str = ", ".join([p.username for p in self.participants.all()[:2]])
        return f"Conversation: {participants_str} - {self.subject or 'No subject'}"

    @property
    def last_message(self):
        return self.messages.first()

    def mark_as_read_for_user(self, user):
        """Mark all messages in this conversation as read for a specific user"""
        self.messages.filter(is_read=False).exclude(sender=user).update(is_read=True, read_at=timezone.now())


class Message(models.Model):
    """Individual message in a conversation"""
    conversation = models.ForeignKey(Conversation, on_delete=models.CASCADE, related_name='messages')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='sent_messages')

    content = models.TextField()

    # Message types
    class MessageType(models.TextChoices):
        TEXT = "text", "Text"
        IMAGE = "image", "Image"
        FILE = "file", "File"
        SYSTEM = "system", "System Message"
        ORDER_UPDATE = "order_update", "Order Update"

    message_type = models.CharField(max_length=20, choices=MessageType.choices, default=MessageType.TEXT)

    # File attachments
    attachment_url = models.URLField(blank=True)
    attachment_name = models.CharField(max_length=255, blank=True)
    attachment_size = models.PositiveIntegerField(null=True, blank=True)  # in bytes

    # Message status
    is_read = models.BooleanField(default=False)
    is_edited = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)
    edited_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['conversation', 'created_at']),
            models.Index(fields=['sender', 'created_at']),
        ]

    def __str__(self):
        return f"Message from {self.sender.username} in {self.conversation.id}"

    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])


class SupportTicket(models.Model):
    """Support tickets for customer service"""
    class Priority(models.TextChoices):
        LOW = "low", "Low"
        MEDIUM = "medium", "Medium"
        HIGH = "high", "High"
        URGENT = "urgent", "Urgent"

    class Status(models.TextChoices):
        OPEN = "open", "Open"
        IN_PROGRESS = "in_progress", "In Progress"
        WAITING_CUSTOMER = "waiting_customer", "Waiting for Customer"
        RESOLVED = "resolved", "Resolved"
        CLOSED = "closed", "Closed"

    class Category(models.TextChoices):
        GENERAL = "general", "General Inquiry"
        ORDER_ISSUE = "order_issue", "Order Issue"
        PAYMENT_ISSUE = "payment_issue", "Payment Issue"
        TECHNICAL = "technical", "Technical Support"
        ACCOUNT = "account", "Account Issue"
        REFUND = "refund", "Refund Request"
        REPORT_USER = "report_user", "Report User"
        REPORT_PRODUCT = "report_product", "Report Product"
        BUG_REPORT = "bug_report", "Bug Report"
        FEATURE_REQUEST = "feature_request", "Feature Request"

    ticket_number = models.CharField(max_length=20, unique=True)
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='support_tickets')
    assigned_agent = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tickets')

    conversation = models.OneToOneField(Conversation, on_delete=models.CASCADE, related_name='support_ticket')

    subject = models.CharField(max_length=200)
    category = models.CharField(max_length=30, choices=Category.choices, default=Category.GENERAL)
    priority = models.CharField(max_length=10, choices=Priority.choices, default=Priority.MEDIUM)
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.OPEN)

    # Related objects
    related_order = models.ForeignKey('orders.Order', on_delete=models.SET_NULL, null=True, blank=True)
    related_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='reported_tickets')

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)
    closed_at = models.DateTimeField(null=True, blank=True)

    # SLA tracking
    first_response_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['status', 'priority']),
            models.Index(fields=['customer', 'status']),
            models.Index(fields=['assigned_agent', 'status']),
        ]

    def __str__(self):
        return f"Ticket #{self.ticket_number} - {self.subject}"

    def save(self, *args, **kwargs):
        if not self.ticket_number:
            # Generate unique ticket number
            import uuid
            self.ticket_number = f"TK{str(uuid.uuid4())[:8].upper()}"
        super().save(*args, **kwargs)
