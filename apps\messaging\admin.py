from django.contrib import admin
from .models import Conversation, Message, SupportTicket


@admin.register(Conversation)
class ConversationAdmin(admin.ModelAdmin):
    list_display = ['id', 'subject', 'is_support_ticket', 'is_archived', 'created_at']
    list_filter = ['is_support_ticket', 'is_archived', 'created_at']
    search_fields = ['subject']
    filter_horizontal = ['participants']


@admin.register(Message)
class MessageAdmin(admin.ModelAdmin):
    list_display = ['conversation', 'sender', 'message_type', 'is_read', 'created_at']
    list_filter = ['message_type', 'is_read', 'created_at']
    search_fields = ['content', 'sender__username']


@admin.register(SupportTicket)
class SupportTicketAdmin(admin.ModelAdmin):
    list_display = ['ticket_number', 'customer', 'category', 'priority', 'status', 'created_at']
    list_filter = ['category', 'priority', 'status', 'created_at']
    search_fields = ['ticket_number', 'subject', 'customer__username']
    readonly_fields = ['ticket_number', 'created_at']
