
from rest_framework import serializers
from .models import Order, OrderItem

class OrderItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrderItem
        fields = ["id","product","service","quantity","price","subtotal"]
        read_only_fields = ["subtotal"]

class OrderSerializer(serializers.ModelSerializer):
    items = OrderItemSerializer(many=True)
    class Meta:
        model = Order
        fields = ["id","customer","status","total","created_at","items"]
        read_only_fields = ["customer","total","created_at"]
    def create(self, validated_data):
        items = validated_data.pop("items", [])
        order = Order.objects.create(**validated_data)
        for it in items:
            OrderItem.objects.create(order=order, **it)
        order.recalc()
        return order
