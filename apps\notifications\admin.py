from django.contrib import admin
from .models import Notification, NotificationPreference, EmailTemplate, SMSTemplate


@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ['title', 'recipient', 'notification_type', 'is_read', 'created_at']
    list_filter = ['notification_type', 'is_read', 'created_at']
    search_fields = ['title', 'recipient__username']
    readonly_fields = ['created_at', 'read_at']


@admin.register(NotificationPreference)
class NotificationPreferenceAdmin(admin.ModelAdmin):
    list_display = ['user', 'email_order_updates', 'sms_order_updates', 'push_order_updates']
    search_fields = ['user__username']


@admin.register(EmailTemplate)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ['notification_type', 'subject', 'is_active']
    list_filter = ['notification_type', 'is_active']


@admin.register(SMSTemplate)
class SMSTemplateAdmin(admin.ModelAdmin):
    list_display = ['notification_type', 'content', 'is_active']
    list_filter = ['notification_type', 'is_active']
