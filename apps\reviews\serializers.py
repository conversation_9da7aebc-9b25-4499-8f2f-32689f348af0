
from rest_framework import serializers
from .models import Review

class ReviewSerializer(serializers.ModelSerializer):
    author_name = serializers.CharField(source='author.username', read_only=True)
    class Meta:
        model = Review
        fields = ['id','author','author_name','product','service','rating','title','body','created_at']
        read_only_fields = ['author','author_name','created_at']
