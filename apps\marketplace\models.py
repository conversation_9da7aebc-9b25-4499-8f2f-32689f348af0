
from django.db import models
from django.conf import settings

class Category(models.Model):
    name = models.CharField(max_length=120)
    def __str__(self): return self.name

class Store(models.Model):
    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="stores")
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    logo = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    def __str__(self): return self.name

class BaseListing(models.Model):
    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name="%(class)ss")
    title = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)
    image = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    class Meta:
        abstract = True

class Product(BaseListing):
    stock = models.PositiveIntegerField(default=0)

class Service(BaseListing):
    delivery_time_days = models.PositiveIntegerField(default=3)
