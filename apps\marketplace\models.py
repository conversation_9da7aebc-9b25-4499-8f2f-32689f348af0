
from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal


class Category(models.Model):
    name = models.CharField(max_length=120)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    description = models.TextField(blank=True)
    image = models.URLField(blank=True)
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=0)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['sort_order', 'name']

    def __str__(self):
        return self.name

    @property
    def full_path(self):
        if self.parent:
            return f"{self.parent.full_path} > {self.name}"
        return self.name


class StoreSubscriptionPlan(models.Model):
    """Subscription plans for stores"""
    name = models.Char<PERSON>ield(max_length=100)
    description = models.TextField()
    price_monthly = models.DecimalField(max_digits=8, decimal_places=2)
    price_yearly = models.DecimalField(max_digits=8, decimal_places=2)

    # Plan limits
    max_products = models.PositiveIntegerField(null=True, blank=True, help_text="Null means unlimited")
    max_services = models.PositiveIntegerField(null=True, blank=True, help_text="Null means unlimited")
    max_images_per_product = models.PositiveIntegerField(default=5)
    commission_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('5.00'))

    # Features
    can_use_coupons = models.BooleanField(default=True)
    can_create_flash_sales = models.BooleanField(default=False)
    priority_support = models.BooleanField(default=False)
    analytics_access = models.BooleanField(default=True)
    custom_domain = models.BooleanField(default=False)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['price_monthly']

    def __str__(self):
        return self.name


class Store(models.Model):
    class VerificationStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        VERIFIED = "verified", "Verified"
        REJECTED = "rejected", "Rejected"
        SUSPENDED = "suspended", "Suspended"

    owner = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="stores")
    name = models.CharField(max_length=200)
    slug = models.SlugField(unique=True, blank=True)
    description = models.TextField(blank=True)
    logo = models.URLField(blank=True)
    banner_image = models.URLField(blank=True)

    # Contact information
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    website = models.URLField(blank=True)

    # Address
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Business information
    business_registration_number = models.CharField(max_length=100, blank=True)
    tax_id = models.CharField(max_length=50, blank=True)

    # Store settings
    is_active = models.BooleanField(default=True)
    verification_status = models.CharField(max_length=20, choices=VerificationStatus.choices, default=VerificationStatus.PENDING)
    verification_document = models.URLField(blank=True)

    # Subscription
    subscription_plan = models.ForeignKey(StoreSubscriptionPlan, on_delete=models.SET_NULL, null=True, blank=True)
    subscription_expires_at = models.DateTimeField(null=True, blank=True)

    # Policies
    return_policy = models.TextField(blank=True)
    shipping_policy = models.TextField(blank=True)
    privacy_policy = models.TextField(blank=True)

    # Social media
    facebook_url = models.URLField(blank=True)
    twitter_url = models.URLField(blank=True)
    instagram_url = models.URLField(blank=True)

    # Metrics
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_orders = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['owner']),
            models.Index(fields=['verification_status']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_subscription_active(self):
        if not self.subscription_expires_at:
            return False
        return timezone.now() < self.subscription_expires_at

    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            import uuid
            self.slug = f"{slugify(self.name)}-{str(uuid.uuid4())[:8]}"
        super().save(*args, **kwargs)

class BaseListing(models.Model):
    class Status(models.TextChoices):
        DRAFT = "draft", "Draft"
        PENDING = "pending", "Pending Review"
        ACTIVE = "active", "Active"
        REJECTED = "rejected", "Rejected"
        SUSPENDED = "suspended", "Suspended"

    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name="%(class)ss")
    title = models.CharField(max_length=200)
    slug = models.SlugField(blank=True)
    description = models.TextField(blank=True)
    short_description = models.CharField(max_length=500, blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    compare_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Original price for discount display")
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True)

    # SEO fields
    meta_title = models.CharField(max_length=60, blank=True)
    meta_description = models.CharField(max_length=160, blank=True)

    # Status and visibility
    status = models.CharField(max_length=20, choices=Status.choices, default=Status.DRAFT)
    is_featured = models.BooleanField(default=False)
    is_digital = models.BooleanField(default=False)

    # Metrics
    view_count = models.PositiveIntegerField(default=0)
    sales_count = models.PositiveIntegerField(default=0)
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))

    # Tags for search and filtering
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['store', 'status']),
            models.Index(fields=['category', 'status']),
            models.Index(fields=['is_featured', 'status']),
        ]

    def save(self, *args, **kwargs):
        if not self.slug:
            from django.utils.text import slugify
            import uuid
            self.slug = f"{slugify(self.title)}-{str(uuid.uuid4())[:8]}"
        super().save(*args, **kwargs)

    @property
    def discount_percentage(self):
        if self.compare_price and self.compare_price > self.price:
            return round(((self.compare_price - self.price) / self.compare_price) * 100, 2)
        return 0

    @property
    def is_on_sale(self):
        return self.compare_price and self.compare_price > self.price


class Product(BaseListing):
    # Inventory
    stock = models.PositiveIntegerField(default=0)
    low_stock_threshold = models.PositiveIntegerField(default=5)
    track_inventory = models.BooleanField(default=True)
    allow_backorders = models.BooleanField(default=False)

    # Physical properties
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Weight in kg")
    length = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Length in cm")
    width = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Width in cm")
    height = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True, help_text="Height in cm")

    # Shipping
    requires_shipping = models.BooleanField(default=True)
    shipping_class = models.CharField(max_length=50, blank=True)

    # Product type
    product_type = models.CharField(max_length=50, default="simple", choices=[
        ("simple", "Simple Product"),
        ("variable", "Variable Product"),
        ("digital", "Digital Product"),
        ("subscription", "Subscription Product"),
    ])

    # Vendor/Brand
    brand = models.CharField(max_length=100, blank=True)
    manufacturer = models.CharField(max_length=100, blank=True)
    model_number = models.CharField(max_length=100, blank=True)

    @property
    def is_in_stock(self):
        if not self.track_inventory:
            return True
        return self.stock > 0 or self.allow_backorders

    @property
    def is_low_stock(self):
        if not self.track_inventory:
            return False
        return self.stock <= self.low_stock_threshold

    def __str__(self):
        return self.title


class ProductVariant(models.Model):
    """Product variants for different options like size, color, etc."""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='variants')

    # Variant details
    title = models.CharField(max_length=200)
    sku = models.CharField(max_length=100, unique=True, blank=True)

    # Pricing
    price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    compare_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Inventory
    stock = models.PositiveIntegerField(default=0)

    # Physical properties
    weight = models.DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)

    # Variant options (stored as JSON)
    options = models.JSONField(default=dict, help_text="e.g., {'color': 'red', 'size': 'large'}")

    # Images
    image = models.URLField(blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['product', 'options']

    def __str__(self):
        return f"{self.product.title} - {self.title}"

    @property
    def effective_price(self):
        return self.price if self.price is not None else self.product.price


class Service(BaseListing):
    class ServiceType(models.TextChoices):
        ONE_TIME = "one_time", "One-time Service"
        RECURRING = "recurring", "Recurring Service"
        CONSULTATION = "consultation", "Consultation"
        CUSTOM = "custom", "Custom Project"

    service_type = models.CharField(max_length=20, choices=ServiceType.choices, default=ServiceType.ONE_TIME)

    # Delivery and timing
    delivery_time_days = models.PositiveIntegerField(default=3)
    max_delivery_time_days = models.PositiveIntegerField(null=True, blank=True)

    # Service details
    includes = models.TextField(blank=True, help_text="What's included in this service")
    requirements = models.TextField(blank=True, help_text="What the customer needs to provide")

    # Pricing for different packages
    basic_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    standard_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)
    premium_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True)

    # Service availability
    is_available_24_7 = models.BooleanField(default=False)
    timezone = models.CharField(max_length=50, blank=True)

    # Skills required
    skills_required = models.CharField(max_length=500, blank=True, help_text="Comma-separated skills")

    # Booking settings
    requires_consultation = models.BooleanField(default=False)
    auto_accept_orders = models.BooleanField(default=True)

    def __str__(self):
        return self.title


class ServicePackage(models.Model):
    """Different packages for a service (Basic, Standard, Premium)"""
    class PackageType(models.TextChoices):
        BASIC = "basic", "Basic"
        STANDARD = "standard", "Standard"
        PREMIUM = "premium", "Premium"

    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='packages')
    package_type = models.CharField(max_length=20, choices=PackageType.choices)

    name = models.CharField(max_length=100)
    description = models.TextField()
    price = models.DecimalField(max_digits=10, decimal_places=2)
    delivery_time_days = models.PositiveIntegerField()

    # Features included
    features = models.JSONField(default=list, help_text="List of features included")

    # Limits
    revisions_included = models.PositiveIntegerField(default=1)

    is_active = models.BooleanField(default=True)

    class Meta:
        unique_together = ['service', 'package_type']

    def __str__(self):
        return f"{self.service.title} - {self.name}"


class ProductImage(models.Model):
    """Multiple images for products"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='images')
    image_url = models.URLField()
    alt_text = models.CharField(max_length=200, blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_primary = models.BooleanField(default=False)

    class Meta:
        ordering = ['sort_order']

    def __str__(self):
        return f"Image for {self.product.title}"


class ServiceImage(models.Model):
    """Multiple images for services"""
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='images')
    image_url = models.URLField()
    alt_text = models.CharField(max_length=200, blank=True)
    sort_order = models.PositiveIntegerField(default=0)
    is_primary = models.BooleanField(default=False)

    class Meta:
        ordering = ['sort_order']

    def __str__(self):
        return f"Image for {self.service.title}"


class Wishlist(models.Model):
    """Customer wishlists"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='wishlists')
    name = models.CharField(max_length=100, default="My Wishlist")
    is_public = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.user.username}'s {self.name}"


class WishlistItem(models.Model):
    """Items in wishlists"""
    wishlist = models.ForeignKey(Wishlist, on_delete=models.CASCADE, related_name='items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, null=True, blank=True)
    service = models.ForeignKey(Service, on_delete=models.CASCADE, null=True, blank=True)
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = [
            ['wishlist', 'product'],
            ['wishlist', 'service'],
        ]

    def __str__(self):
        item = self.product or self.service
        return f"{item.title} in {self.wishlist.name}"


class StoreFollower(models.Model):
    """Users following stores"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='followed_stores')
    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name='followers')
    followed_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'store']

    def __str__(self):
        return f"{self.user.username} follows {self.store.name}"


class ProductAttribute(models.Model):
    """Product attributes like color, size, material, etc."""
    name = models.CharField(max_length=100, unique=True)
    display_name = models.CharField(max_length=100)
    attribute_type = models.CharField(max_length=20, choices=[
        ('text', 'Text'),
        ('number', 'Number'),
        ('color', 'Color'),
        ('boolean', 'Yes/No'),
        ('select', 'Select'),
    ], default='text')

    # For select type attributes
    options = models.JSONField(default=list, blank=True, help_text="Available options for select type")

    is_required = models.BooleanField(default=False)
    is_variation = models.BooleanField(default=False, help_text="Can be used for product variations")

    def __str__(self):
        return self.display_name


class ProductAttributeValue(models.Model):
    """Values for product attributes"""
    product = models.ForeignKey(Product, on_delete=models.CASCADE, related_name='attribute_values')
    attribute = models.ForeignKey(ProductAttribute, on_delete=models.CASCADE)
    value = models.CharField(max_length=200)

    class Meta:
        unique_together = ['product', 'attribute']

    def __str__(self):
        return f"{self.product.title} - {self.attribute.display_name}: {self.value}"
