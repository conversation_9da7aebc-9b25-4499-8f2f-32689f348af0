from django.contrib import admin
from .models import (
    StoreAnalytics, ProductAnalytics, ServiceAnalytics, UserAnalytics,
    DailyMetrics, SearchAnalytics, PageView
)


@admin.register(StoreAnalytics)
class StoreAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['store', 'total_sales', 'total_orders', 'average_rating', 'last_updated']
    search_fields = ['store__name']
    readonly_fields = ['last_updated']


@admin.register(ProductAnalytics)
class ProductAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['product', 'total_sales', 'units_sold', 'total_views', 'conversion_rate']
    search_fields = ['product__title']


@admin.register(ServiceAnalytics)
class ServiceAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['service', 'total_sales', 'total_bookings', 'average_rating']
    search_fields = ['service__title']


@admin.register(UserAnalytics)
class UserAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['user', 'total_orders', 'total_spent', 'total_sales', 'login_count']
    search_fields = ['user__username']


@admin.register(DailyMetrics)
class DailyMetricsAdmin(admin.ModelAdmin):
    list_display = ['date', 'total_sales', 'total_orders', 'new_users', 'active_users']
    list_filter = ['date']
    ordering = ['-date']


@admin.register(SearchAnalytics)
class SearchAnalyticsAdmin(admin.ModelAdmin):
    list_display = ['query', 'results_count', 'user', 'created_at']
    list_filter = ['created_at']
    search_fields = ['query']


@admin.register(PageView)
class PageViewAdmin(admin.ModelAdmin):
    list_display = ['page_type', 'page_url', 'user', 'created_at']
    list_filter = ['page_type', 'created_at']
