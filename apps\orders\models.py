
from django.db import models
from django.conf import settings
from apps.marketplace.models import Product, Service

class Order(models.Model):
    customer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="orders")
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=30, default="pending")
    total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    def __str__(self): return f"Order {self.id}"
    def recalc(self):
        self.total = sum(item.subtotal for item in self.items.all())
        self.save(update_fields=["total"])

class OrderItem(models.Model):
    order = models.ForeignKey(Order, on_delete=models.CASCADE, related_name="items")
    product = models.ForeignKey(Product, null=True, blank=True, on_delete=models.SET_NULL)
    service = models.ForeignKey(Service, null=True, blank=True, on_delete=models.SET_NULL)
    quantity = models.PositiveIntegerField(default=1)
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    @property
    def subtotal(self): return (self.price or 0) * self.quantity
