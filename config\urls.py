from django.contrib import admin
from django.urls import path, include, include
from rest_framework import routers
from apps.users.api import AuthViewSet
from rest_framework_simplejwt.views import TokenObtainPairView, TokenRefreshView

from apps.marketplace.api import StoreViewSet, ProductViewSet, ServiceViewSet, CategoryViewSet, SearchViewSet
from apps.orders.api import OrderViewSet
from apps.reviews.api import ReviewViewSet

router = routers.DefaultRouter()
router.register(r"auth", AuthViewSet, basename="auth")
router.register(r"stores", StoreViewSet)
router.register(r"categories", CategoryViewSet)
router.register(r"products", ProductViewSet)
router.register(r"services", ServiceViewSet)
router.register(r"search", SearchViewSet, basename="search")
router.register(r"orders", OrderViewSet)
router.register(r"reviews", ReviewViewSet)
router.register(r"reviews", include := None)

urlpatterns = [
    path('api/v1/token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/v1/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),

    path("admin/", admin.site.urls),
    path("api/v1/", include(router.urls)),
]

urlpatterns += [ path("api/v1/accounts/", include("apps.accounts.urls")), ]
