
import os, json, logging
import stripe
from django.conf import settings
from django.http import JsonResponse, HttpResponse
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticatedOrReadOnly, IsAuthenticated
from rest_framework.response import Response
from apps.marketplace.models import Product, Service
from apps.orders.models import Order, OrderItem
from django.contrib.auth import get_user_model

stripe.api_key = os.getenv('STRIPE_SECRET_KEY', settings.STRIPE_SECRET_KEY)
User = get_user_model()

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_checkout_session(request):
    data = request.data
    items = data.get('items', [])
    line_items = []
    for it in items:
        if it.get('type') == 'product':
            try:
                p = Product.objects.get(id=it['id'])
            except Product.DoesNotExist:
                continue
            line_items.append({
                'price_data': {
                    'currency': 'usd',
                    'product_data': {'name': p.title},
                    'unit_amount': int(float(p.price) * 100),
                },
                'quantity': int(it.get('quantity',1))
            })
        else:
            try:
                s = Service.objects.get(id=it['id'])
            except Service.DoesNotExist:
                continue
            line_items.append({
                'price_data': {
                    'currency': 'usd',
                    'product_data': {'name': s.title},
                    'unit_amount': int(float(s.price) * 100),
                },
                'quantity': int(it.get('quantity',1))
            })
    try:
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            mode='payment',
            line_items=line_items,
            success_url=data.get('success_url') or 'http://localhost:3000/checkout/success',
            cancel_url=data.get('cancel_url') or 'http://localhost:3000/checkout/cancel',
            metadata={'user_id': str(request.user.id), 'order_id': str(data.get('order_id',''))}
        )
        return Response({'url': checkout_session.url, 'id': checkout_session.id})
    except Exception as e:
        logging.exception("Stripe create checkout error")
        return Response({'error': str(e)}, status=400)

@csrf_exempt
def stripe_webhook(request):
    payload = request.body
    sig_header = request.META.get('HTTP_STRIPE_SIGNATURE', '')
    endpoint_secret = os.getenv('STRIPE_WEBHOOK_SECRET', settings.STRIPE_WEBHOOK_SECRET)
    try:
        if endpoint_secret:
            evt = stripe.Webhook.construct_event(payload, sig_header, endpoint_secret)
        else:
            evt = json.loads(payload)
    except Exception as e:
        return HttpResponse(status=400)
    # Handle checkout.session.completed
    evt_type = evt.get('type')
    data = evt.get('data', {}).get('object', {})
    if evt_type in ('checkout.session.completed', 'checkout.session.async_payment_succeeded'):
        session = data
        handle_checkout_completed(session)
    return HttpResponse(status=200)

def handle_checkout_completed(session):
    metadata = session.get('metadata', {}) or {}
    order_id = metadata.get('order_id')
    user_id = metadata.get('user_id')
    user = None
    try:
        if user_id:
            user = User.objects.filter(id=int(user_id)).first()
    except Exception:
        user = None
    # If order id provided, mark order paid
    if order_id:
        try:
            order = Order.objects.filter(id=int(order_id)).first()
            if order:
                order.status = 'paid'
                order.save(update_fields=['status'])
                return
        except Exception:
            pass
    # Fallback: create basic order from session's line_items or display_items
    try:
        order = Order.objects.create(customer=user if user else None, status='paid', total=0)
        # Stripe may not include line_items in webhook payload unless expanded. Try display_items or line_items.
        items = session.get('display_items') or session.get('line_items') or []
        for li in items:
            # description or price_data
            desc = li.get('description') or (li.get('price_data') or {}).get('product_data', {}).get('name')
            amount = (li.get('amount') or (li.get('price_data') or {}).get('unit_amount', 0)) / 100.0
            qty = li.get('quantity', 1)
            from apps.marketplace.models import Product, Service
            p = Product.objects.filter(title__iexact=desc).first() if desc else None
            s = None if p else (Service.objects.filter(title__iexact=desc).first() if desc else None)
            item = OrderItem.objects.create(order=order, product=p, service=s, quantity=qty, price=amount)
            if p and hasattr(p, 'stock'):
                try:
                    if p.stock and p.stock >= qty:
                        p.stock = p.stock - qty
                        p.save(update_fields=['stock'])
                except Exception:
                    pass
        order.recalc()
    except Exception as e:
        print('Error creating order from session:', e)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def create_onboarding_link(request):
    try:
        user = request.user
        from .models import SellerStripeAccount
        _stripe = stripe
        if not getattr(user, 'email', None):
            return Response({'error': 'user has no email'}, status=400)
        s_account = SellerStripeAccount.objects.filter(user=user).first()
        if not s_account or not s_account.stripe_account_id:
            acct = _stripe.Account.create(type='express', country='US', email=user.email)
            if not s_account:
                s_account = SellerStripeAccount.objects.create(user=user, stripe_account_id=acct.id)
            else:
                s_account.stripe_account_id = acct.id
                s_account.save(update_fields=['stripe_account_id'])
        acct_id = s_account.stripe_account_id
        link = _stripe.Account.create_login_link(acct_id)
        return Response({'url': link.url})
    except Exception as e:
        logging.exception('Error creating onboarding link')
        return Response({'error': str(e)}, status=400)
