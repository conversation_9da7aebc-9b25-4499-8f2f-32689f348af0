# Generated by Django 4.2.23 on 2025-08-29 02:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('order_placed', 'Order Placed'), ('order_confirmed', 'Order Confirmed'), ('order_shipped', 'Order Shipped'), ('order_delivered', 'Order Delivered'), ('order_cancelled', 'Order Cancelled'), ('payment_received', 'Payment Received'), ('review_received', 'Review Received'), ('message_received', 'Message Received'), ('store_approved', 'Store Approved'), ('store_rejected', 'Store Rejected'), ('product_approved', 'Product Approved'), ('product_rejected', 'Product Rejected'), ('service_booked', 'Service Booked'), ('payout_processed', 'Payout Processed'), ('account_verified', 'Account Verified'), ('promotion_started', 'Promotion Started'), ('low_stock', 'Low Stock Alert'), ('system_announcement', 'System Announcement')], max_length=30, unique=True)),
                ('subject', models.CharField(max_length=200)),
                ('html_content', models.TextField()),
                ('text_content', models.TextField(blank=True)),
                ('variables', models.JSONField(default=dict, help_text='Available template variables')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='SMSTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('order_placed', 'Order Placed'), ('order_confirmed', 'Order Confirmed'), ('order_shipped', 'Order Shipped'), ('order_delivered', 'Order Delivered'), ('order_cancelled', 'Order Cancelled'), ('payment_received', 'Payment Received'), ('review_received', 'Review Received'), ('message_received', 'Message Received'), ('store_approved', 'Store Approved'), ('store_rejected', 'Store Rejected'), ('product_approved', 'Product Approved'), ('product_rejected', 'Product Rejected'), ('service_booked', 'Service Booked'), ('payout_processed', 'Payout Processed'), ('account_verified', 'Account Verified'), ('promotion_started', 'Promotion Started'), ('low_stock', 'Low Stock Alert'), ('system_announcement', 'System Announcement')], max_length=30, unique=True)),
                ('content', models.CharField(max_length=160)),
                ('variables', models.JSONField(default=dict, help_text='Available template variables')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='NotificationPreference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('email_order_updates', models.BooleanField(default=True)),
                ('email_payment_updates', models.BooleanField(default=True)),
                ('email_review_notifications', models.BooleanField(default=True)),
                ('email_message_notifications', models.BooleanField(default=True)),
                ('email_marketing', models.BooleanField(default=False)),
                ('email_system_announcements', models.BooleanField(default=True)),
                ('sms_order_updates', models.BooleanField(default=False)),
                ('sms_payment_updates', models.BooleanField(default=False)),
                ('sms_security_alerts', models.BooleanField(default=True)),
                ('push_order_updates', models.BooleanField(default=True)),
                ('push_message_notifications', models.BooleanField(default=True)),
                ('push_marketing', models.BooleanField(default=False)),
                ('in_app_all', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='notification_preferences', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('order_placed', 'Order Placed'), ('order_confirmed', 'Order Confirmed'), ('order_shipped', 'Order Shipped'), ('order_delivered', 'Order Delivered'), ('order_cancelled', 'Order Cancelled'), ('payment_received', 'Payment Received'), ('review_received', 'Review Received'), ('message_received', 'Message Received'), ('store_approved', 'Store Approved'), ('store_rejected', 'Store Rejected'), ('product_approved', 'Product Approved'), ('product_rejected', 'Product Rejected'), ('service_booked', 'Service Booked'), ('payout_processed', 'Payout Processed'), ('account_verified', 'Account Verified'), ('promotion_started', 'Promotion Started'), ('low_stock', 'Low Stock Alert'), ('system_announcement', 'System Announcement')], max_length=30)),
                ('title', models.CharField(max_length=200)),
                ('message', models.TextField()),
                ('object_id', models.PositiveIntegerField(blank=True, null=True)),
                ('is_read', models.BooleanField(default=False)),
                ('is_sent_email', models.BooleanField(default=False)),
                ('is_sent_sms', models.BooleanField(default=False)),
                ('is_sent_push', models.BooleanField(default=False)),
                ('data', models.JSONField(blank=True, default=dict)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('content_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['recipient', 'is_read'], name='notificatio_recipie_4e3567_idx'), models.Index(fields=['recipient', 'created_at'], name='notificatio_recipie_f39341_idx')],
            },
        ),
    ]
