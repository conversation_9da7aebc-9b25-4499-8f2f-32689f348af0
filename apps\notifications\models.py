from django.db import models
from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.contrib.contenttypes.fields import GenericForeignKey


class Notification(models.Model):
    class NotificationType(models.TextChoices):
        ORDER_PLACED = "order_placed", "Order Placed"
        ORDER_CONFIRMED = "order_confirmed", "Order Confirmed"
        ORDER_SHIPPED = "order_shipped", "Order Shipped"
        ORDER_DELIVERED = "order_delivered", "Order Delivered"
        ORDER_CANCELLED = "order_cancelled", "Order Cancelled"
        PAYMENT_RECEIVED = "payment_received", "Payment Received"
        REVIEW_RECEIVED = "review_received", "Review Received"
        MESSAGE_RECEIVED = "message_received", "Message Received"
        STORE_APPROVED = "store_approved", "Store Approved"
        STORE_REJECTED = "store_rejected", "Store Rejected"
        PRODUCT_APPROVED = "product_approved", "Product Approved"
        PRODUCT_REJECTED = "product_rejected", "Product Rejected"
        SERVICE_BOOKED = "service_booked", "Service Booked"
        PAYOUT_PROCESSED = "payout_processed", "Payout Processed"
        ACCOUNT_VERIFIED = "account_verified", "Account Verified"
        PROMOTION_STARTED = "promotion_started", "Promotion Started"
        LOW_STOCK = "low_stock", "Low Stock Alert"
        SYSTEM_ANNOUNCEMENT = "system_announcement", "System Announcement"

    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications')
    notification_type = models.CharField(max_length=30, choices=NotificationType.choices)
    title = models.CharField(max_length=200)
    message = models.TextField()

    # Generic relation to any model
    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE, null=True, blank=True)
    object_id = models.PositiveIntegerField(null=True, blank=True)
    content_object = GenericForeignKey('content_type', 'object_id')

    # Notification status
    is_read = models.BooleanField(default=False)
    is_sent_email = models.BooleanField(default=False)
    is_sent_sms = models.BooleanField(default=False)
    is_sent_push = models.BooleanField(default=False)

    # Metadata
    data = models.JSONField(default=dict, blank=True)  # Additional data for the notification

    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['recipient', 'is_read']),
            models.Index(fields=['recipient', 'created_at']),
        ]

    def __str__(self):
        return f"{self.title} - {self.recipient.username}"

    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save(update_fields=['is_read', 'read_at'])


class NotificationPreference(models.Model):
    """User preferences for different types of notifications"""
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notification_preferences')

    # Email preferences
    email_order_updates = models.BooleanField(default=True)
    email_payment_updates = models.BooleanField(default=True)
    email_review_notifications = models.BooleanField(default=True)
    email_message_notifications = models.BooleanField(default=True)
    email_marketing = models.BooleanField(default=False)
    email_system_announcements = models.BooleanField(default=True)

    # SMS preferences
    sms_order_updates = models.BooleanField(default=False)
    sms_payment_updates = models.BooleanField(default=False)
    sms_security_alerts = models.BooleanField(default=True)

    # Push notification preferences
    push_order_updates = models.BooleanField(default=True)
    push_message_notifications = models.BooleanField(default=True)
    push_marketing = models.BooleanField(default=False)

    # In-app preferences
    in_app_all = models.BooleanField(default=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Notification preferences for {self.user.username}"


class EmailTemplate(models.Model):
    """Email templates for different notification types"""
    notification_type = models.CharField(max_length=30, choices=Notification.NotificationType.choices, unique=True)
    subject = models.CharField(max_length=200)
    html_content = models.TextField()
    text_content = models.TextField(blank=True)

    # Template variables documentation
    variables = models.JSONField(default=dict, help_text="Available template variables")

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Email template for {self.get_notification_type_display()}"


class SMSTemplate(models.Model):
    """SMS templates for different notification types"""
    notification_type = models.CharField(max_length=30, choices=Notification.NotificationType.choices, unique=True)
    content = models.CharField(max_length=160)  # SMS character limit

    # Template variables documentation
    variables = models.JSONField(default=dict, help_text="Available template variables")

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"SMS template for {self.get_notification_type_display()}"
