
from django.db import models
from django.conf import settings
from apps.marketplace.models import Product, Service

class Review(models.Model):
    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    product = models.ForeignKey(Product, related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)
    service = models.ForeignKey(Service, related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)
    rating = models.PositiveSmallIntegerField(default=5)
    title = models.CharField(max_length=200, blank=True)
    body = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    class Meta:
        ordering = ['-created_at']
    def __str__(self): return f"Review {self.id} by {self.author_id}"
