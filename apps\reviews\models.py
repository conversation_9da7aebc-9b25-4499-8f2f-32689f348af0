
from django.db import models
from django.conf import settings
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from apps.marketplace.models import Product, Service, Store


class Review(models.Model):
    class ReviewStatus(models.TextChoices):
        PENDING = "pending", "Pending Moderation"
        APPROVED = "approved", "Approved"
        REJECTED = "rejected", "Rejected"
        FLAGGED = "flagged", "Flagged"

    author = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='authored_reviews')

    # What is being reviewed
    product = models.ForeignKey(Product, related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)
    service = models.ForeignKey(Service, related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)
    store = models.ForeignKey(Store, related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)
    order = models.ForeignKey('orders.Order', related_name='reviews', null=True, blank=True, on_delete=models.CASCADE)

    # Review content
    rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        help_text="Rating from 1 to 5 stars"
    )
    title = models.CharField(max_length=200, blank=True)
    body = models.TextField(blank=True)

    # Additional ratings for services
    communication_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True,
        help_text="Communication rating for services"
    )
    delivery_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True,
        help_text="Delivery/timeliness rating for services"
    )
    quality_rating = models.PositiveSmallIntegerField(
        validators=[MinValueValidator(1), MaxValueValidator(5)],
        null=True, blank=True,
        help_text="Quality rating for services"
    )

    # Review metadata
    is_verified_purchase = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=ReviewStatus.choices, default=ReviewStatus.PENDING)

    # Moderation
    moderated_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='moderated_reviews'
    )
    moderated_at = models.DateTimeField(null=True, blank=True)
    moderation_notes = models.TextField(blank=True)

    # Engagement
    helpful_count = models.PositiveIntegerField(default=0)
    not_helpful_count = models.PositiveIntegerField(default=0)

    # Images
    images = models.JSONField(default=list, blank=True, help_text="List of image URLs")

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['author', 'created_at']),
            models.Index(fields=['product', 'status']),
            models.Index(fields=['service', 'status']),
            models.Index(fields=['store', 'status']),
            models.Index(fields=['status', 'created_at']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(product__isnull=False) | models.Q(service__isnull=False) | models.Q(store__isnull=False),
                name='review_has_target'
            )
        ]

    def __str__(self):
        target = self.product or self.service or self.store
        return f"Review by {self.author.username} for {target}"

    @property
    def average_service_rating(self):
        """Calculate average rating for services with multiple criteria"""
        if not (self.communication_rating and self.delivery_rating and self.quality_rating):
            return self.rating
        return round((self.communication_rating + self.delivery_rating + self.quality_rating) / 3, 1)

    def approve(self, moderator=None):
        self.status = self.ReviewStatus.APPROVED
        self.moderated_by = moderator
        self.moderated_at = timezone.now()
        self.save(update_fields=['status', 'moderated_by', 'moderated_at'])

    def reject(self, moderator=None, notes=""):
        self.status = self.ReviewStatus.REJECTED
        self.moderated_by = moderator
        self.moderated_at = timezone.now()
        self.moderation_notes = notes
        self.save(update_fields=['status', 'moderated_by', 'moderated_at', 'moderation_notes'])


class ReviewHelpfulness(models.Model):
    """Track if users found reviews helpful"""
    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='helpfulness_votes')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    is_helpful = models.BooleanField()  # True for helpful, False for not helpful
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['review', 'user']
        indexes = [
            models.Index(fields=['review', 'is_helpful']),
        ]

    def __str__(self):
        helpful_text = "helpful" if self.is_helpful else "not helpful"
        return f"{self.user.username} found review {self.review.id} {helpful_text}"


class ReviewResponse(models.Model):
    """Store owner responses to reviews"""
    review = models.OneToOneField(Review, on_delete=models.CASCADE, related_name='response')
    responder = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Response to review {self.review.id} by {self.responder.username}"


class ReviewFlag(models.Model):
    """User reports for inappropriate reviews"""
    class FlagReason(models.TextChoices):
        SPAM = "spam", "Spam"
        INAPPROPRIATE = "inappropriate", "Inappropriate Content"
        FAKE = "fake", "Fake Review"
        OFFENSIVE = "offensive", "Offensive Language"
        OTHER = "other", "Other"

    review = models.ForeignKey(Review, on_delete=models.CASCADE, related_name='flags')
    flagger = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    reason = models.CharField(max_length=20, choices=FlagReason.choices)
    description = models.TextField(blank=True)

    # Moderation
    is_resolved = models.BooleanField(default=False)
    resolved_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True, blank=True,
        related_name='resolved_flags'
    )
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['review', 'flagger']
        indexes = [
            models.Index(fields=['is_resolved', 'created_at']),
        ]

    def __str__(self):
        return f"Flag on review {self.review.id} by {self.flagger.username}"

    def resolve(self, moderator, notes=""):
        self.is_resolved = True
        self.resolved_by = moderator
        self.resolved_at = timezone.now()
        self.resolution_notes = notes
        self.save(update_fields=['is_resolved', 'resolved_by', 'resolved_at', 'resolution_notes'])
