from django.db import models
from django.conf import settings
from django.utils import timezone
from decimal import Decimal


class StoreAnalytics(models.Model):
    """Analytics data for stores"""
    store = models.OneToOneField('marketplace.Store', on_delete=models.CASCADE, related_name='analytics')
    
    # Sales metrics
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_orders = models.PositiveIntegerField(default=0)
    total_products_sold = models.PositiveIntegerField(default=0)
    
    # Customer metrics
    total_customers = models.PositiveIntegerField(default=0)
    returning_customers = models.PositiveIntegerField(default=0)
    
    # Product metrics
    total_products = models.PositiveIntegerField(default=0)
    active_products = models.PositiveIntegerField(default=0)
    
    # Review metrics
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_reviews = models.PositiveIntegerField(default=0)
    
    # Time-based metrics
    sales_this_month = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    sales_last_month = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    orders_this_month = models.PositiveIntegerField(default=0)
    orders_last_month = models.PositiveIntegerField(default=0)
    
    # Conversion metrics
    store_views = models.PositiveIntegerField(default=0)
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analytics for {self.store.name}"


class ProductAnalytics(models.Model):
    """Analytics data for products"""
    product = models.OneToOneField('marketplace.Product', on_delete=models.CASCADE, related_name='analytics')
    
    # Sales metrics
    total_sales = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_orders = models.PositiveIntegerField(default=0)
    units_sold = models.PositiveIntegerField(default=0)
    
    # View metrics
    total_views = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    
    # Conversion metrics
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    # Review metrics
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_reviews = models.PositiveIntegerField(default=0)
    
    # Time-based metrics
    sales_this_month = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    views_this_month = models.PositiveIntegerField(default=0)
    
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analytics for {self.product.title}"


class ServiceAnalytics(models.Model):
    """Analytics data for services"""
    service = models.OneToOneField('marketplace.Service', on_delete=models.CASCADE, related_name='analytics')
    
    # Sales metrics
    total_sales = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_bookings = models.PositiveIntegerField(default=0)
    completed_services = models.PositiveIntegerField(default=0)
    
    # View metrics
    total_views = models.PositiveIntegerField(default=0)
    unique_views = models.PositiveIntegerField(default=0)
    
    # Performance metrics
    average_completion_time = models.DurationField(null=True, blank=True)
    on_time_delivery_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    # Review metrics
    average_rating = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    total_reviews = models.PositiveIntegerField(default=0)
    
    # Conversion metrics
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=Decimal('0.00'))
    
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analytics for {self.service.title}"


class UserAnalytics(models.Model):
    """Analytics data for users"""
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='analytics')
    
    # Customer metrics (for customers)
    total_orders = models.PositiveIntegerField(default=0)
    total_spent = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    average_order_value = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Seller/Freelancer metrics
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_earnings = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    
    # Engagement metrics
    login_count = models.PositiveIntegerField(default=0)
    last_activity = models.DateTimeField(null=True, blank=True)
    
    # Review metrics
    reviews_given = models.PositiveIntegerField(default=0)
    reviews_received = models.PositiveIntegerField(default=0)
    average_rating_received = models.DecimalField(max_digits=3, decimal_places=2, default=Decimal('0.00'))
    
    last_updated = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Analytics for {self.user.username}"


class DailyMetrics(models.Model):
    """Daily platform metrics"""
    date = models.DateField(unique=True)
    
    # Sales metrics
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_orders = models.PositiveIntegerField(default=0)
    
    # User metrics
    new_users = models.PositiveIntegerField(default=0)
    active_users = models.PositiveIntegerField(default=0)
    new_sellers = models.PositiveIntegerField(default=0)
    new_freelancers = models.PositiveIntegerField(default=0)
    
    # Product metrics
    new_products = models.PositiveIntegerField(default=0)
    new_services = models.PositiveIntegerField(default=0)
    
    # Engagement metrics
    total_page_views = models.PositiveIntegerField(default=0)
    unique_visitors = models.PositiveIntegerField(default=0)
    
    # Support metrics
    new_support_tickets = models.PositiveIntegerField(default=0)
    resolved_support_tickets = models.PositiveIntegerField(default=0)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-date']
    
    def __str__(self):
        return f"Metrics for {self.date}"


class SearchAnalytics(models.Model):
    """Track search queries and results"""
    query = models.CharField(max_length=200)
    results_count = models.PositiveIntegerField(default=0)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    
    # Click tracking
    clicked_product = models.ForeignKey('marketplace.Product', on_delete=models.SET_NULL, null=True, blank=True)
    clicked_service = models.ForeignKey('marketplace.Service', on_delete=models.SET_NULL, null=True, blank=True)
    clicked_store = models.ForeignKey('marketplace.Store', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['created_at']),
            models.Index(fields=['user']),
        ]
    
    def __str__(self):
        return f"Search: {self.query} ({self.results_count} results)"


class PageView(models.Model):
    """Track page views for analytics"""
    class PageType(models.TextChoices):
        PRODUCT = "product", "Product"
        SERVICE = "service", "Service"
        STORE = "store", "Store"
        CATEGORY = "category", "Category"
        SEARCH = "search", "Search"
        HOME = "home", "Home"
        OTHER = "other", "Other"
    
    page_type = models.CharField(max_length=20, choices=PageType.choices)
    page_url = models.URLField()
    
    # Related objects
    product = models.ForeignKey('marketplace.Product', on_delete=models.SET_NULL, null=True, blank=True)
    service = models.ForeignKey('marketplace.Service', on_delete=models.SET_NULL, null=True, blank=True)
    store = models.ForeignKey('marketplace.Store', on_delete=models.SET_NULL, null=True, blank=True)
    category = models.ForeignKey('marketplace.Category', on_delete=models.SET_NULL, null=True, blank=True)
    
    # User info
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True)
    session_id = models.CharField(max_length=100, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    
    # Metadata
    user_agent = models.TextField(blank=True)
    referrer = models.URLField(blank=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        indexes = [
            models.Index(fields=['page_type', 'created_at']),
            models.Index(fields=['user', 'created_at']),
            models.Index(fields=['session_id']),
        ]
    
    def __str__(self):
        return f"{self.page_type} view: {self.page_url}"
