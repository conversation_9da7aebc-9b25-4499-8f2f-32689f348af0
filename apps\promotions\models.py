from django.db import models
from django.conf import settings
from django.core.validators import MinV<PERSON>ueValidator, MaxValueValidator
from django.utils import timezone
from decimal import Decimal


class Coupon(models.Model):
    """Discount coupons for orders"""
    class DiscountType(models.TextChoices):
        PERCENTAGE = "percentage", "Percentage"
        FIXED_AMOUNT = "fixed_amount", "Fixed Amount"
        FREE_SHIPPING = "free_shipping", "Free Shipping"
    
    class CouponType(models.TextChoices):
        GENERAL = "general", "General"
        FIRST_ORDER = "first_order", "First Order"
        STORE_SPECIFIC = "store_specific", "Store Specific"
        PRODUCT_SPECIFIC = "product_specific", "Product Specific"
        USER_SPECIFIC = "user_specific", "User Specific"
    
    code = models.Char<PERSON>ield(max_length=50, unique=True)
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Discount details
    discount_type = models.Char<PERSON><PERSON>(max_length=20, choices=DiscountType.choices)
    discount_value = models.DecimalField(max_digits=10, decimal_places=2)
    
    # Coupon type and restrictions
    coupon_type = models.Char<PERSON>ield(max_length=20, choices=CouponType.choices, default=CouponType.GENERAL)
    
    # Usage limits
    usage_limit = models.PositiveIntegerField(null=True, blank=True, help_text="Total usage limit")
    usage_limit_per_user = models.PositiveIntegerField(null=True, blank=True, help_text="Usage limit per user")
    used_count = models.PositiveIntegerField(default=0)
    
    # Minimum order requirements
    minimum_order_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Date restrictions
    valid_from = models.DateTimeField()
    valid_until = models.DateTimeField()
    
    # Specific restrictions
    applicable_stores = models.ManyToManyField('marketplace.Store', blank=True)
    applicable_products = models.ManyToManyField('marketplace.Product', blank=True)
    applicable_categories = models.ManyToManyField('marketplace.Category', blank=True)
    applicable_users = models.ManyToManyField(settings.AUTH_USER_MODEL, blank=True)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    # Creator (for store-specific coupons)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='created_coupons')
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['code']),
            models.Index(fields=['valid_from', 'valid_until']),
            models.Index(fields=['is_active']),
        ]
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    @property
    def is_valid(self):
        now = timezone.now()
        return (self.is_active and 
                self.valid_from <= now <= self.valid_until and
                (self.usage_limit is None or self.used_count < self.usage_limit))
    
    def can_be_used_by_user(self, user):
        if not self.is_valid:
            return False
        
        # Check user-specific restrictions
        if self.coupon_type == self.CouponType.USER_SPECIFIC:
            return self.applicable_users.filter(id=user.id).exists()
        
        # Check usage limit per user
        if self.usage_limit_per_user:
            user_usage = CouponUsage.objects.filter(coupon=self, user=user).count()
            if user_usage >= self.usage_limit_per_user:
                return False
        
        # Check first order restriction
        if self.coupon_type == self.CouponType.FIRST_ORDER:
            from apps.orders.models import Order
            return not Order.objects.filter(customer=user).exists()
        
        return True
    
    def calculate_discount(self, order_amount):
        if self.discount_type == self.DiscountType.PERCENTAGE:
            return min(order_amount * (self.discount_value / 100), order_amount)
        elif self.discount_type == self.DiscountType.FIXED_AMOUNT:
            return min(self.discount_value, order_amount)
        return Decimal('0.00')


class CouponUsage(models.Model):
    """Track coupon usage"""
    coupon = models.ForeignKey(Coupon, on_delete=models.CASCADE, related_name='usages')
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    order = models.ForeignKey('orders.Order', on_delete=models.CASCADE)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2)
    used_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['coupon', 'order']
        indexes = [
            models.Index(fields=['coupon', 'user']),
            models.Index(fields=['used_at']),
        ]
    
    def __str__(self):
        return f"{self.coupon.code} used by {self.user.username}"


class FlashSale(models.Model):
    """Flash sales for products"""
    name = models.CharField(max_length=200)
    description = models.TextField(blank=True)
    
    # Sale details
    discount_percentage = models.DecimalField(
        max_digits=5, 
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    
    # Products in sale
    products = models.ManyToManyField('marketplace.Product', related_name='flash_sales')
    
    # Date restrictions
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    
    # Quantity limits
    max_quantity_per_user = models.PositiveIntegerField(null=True, blank=True)
    total_quantity_limit = models.PositiveIntegerField(null=True, blank=True)
    sold_quantity = models.PositiveIntegerField(default=0)
    
    # Status
    is_active = models.BooleanField(default=True)
    
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        ordering = ['-start_time']
    
    def __str__(self):
        return f"{self.name} - {self.discount_percentage}% off"
    
    @property
    def is_live(self):
        now = timezone.now()
        return (self.is_active and 
                self.start_time <= now <= self.end_time and
                (self.total_quantity_limit is None or self.sold_quantity < self.total_quantity_limit))
    
    @property
    def time_remaining(self):
        if not self.is_live:
            return None
        return self.end_time - timezone.now()


class AffiliateProgram(models.Model):
    """Affiliate marketing program"""
    affiliate = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='affiliate_programs')
    
    # Commission settings
    commission_rate = models.DecimalField(
        max_digits=5, 
        decimal_places=2,
        validators=[MinValueValidator(0), MaxValueValidator(100)],
        help_text="Commission percentage"
    )
    
    # Tracking
    referral_code = models.CharField(max_length=50, unique=True)
    total_referrals = models.PositiveIntegerField(default=0)
    total_sales = models.DecimalField(max_digits=12, decimal_places=2, default=Decimal('0.00'))
    total_commission = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    
    # Status
    is_active = models.BooleanField(default=True)
    
    created_at = models.DateTimeField(auto_now_add=True)
    
    def __str__(self):
        return f"Affiliate: {self.affiliate.username} - {self.referral_code}"


class AffiliateReferral(models.Model):
    """Track affiliate referrals"""
    affiliate_program = models.ForeignKey(AffiliateProgram, on_delete=models.CASCADE, related_name='referrals')
    referred_user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='referrals')
    order = models.ForeignKey('orders.Order', on_delete=models.CASCADE, null=True, blank=True)
    
    commission_amount = models.DecimalField(max_digits=10, decimal_places=2, default=Decimal('0.00'))
    is_commission_paid = models.BooleanField(default=False)
    
    created_at = models.DateTimeField(auto_now_add=True)
    commission_paid_at = models.DateTimeField(null=True, blank=True)
    
    def __str__(self):
        return f"Referral by {self.affiliate_program.affiliate.username} -> {self.referred_user.username}"
