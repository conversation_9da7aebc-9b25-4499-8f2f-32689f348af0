
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.core.validators import RegexValidator
from django.utils import timezone

class User(AbstractUser):
    class Roles(models.TextChoices):
        CUSTOMER = "customer","Customer"
        SELLER = "seller","Seller"
        FREELANCER = "freelancer","Freelancer"
        ADMIN = "admin","Admin"

    class VerificationStatus(models.TextChoices):
        PENDING = "pending", "Pending"
        VERIFIED = "verified", "Verified"
        REJECTED = "rejected", "Rejected"

    role = models.CharField(max_length=20, choices=Roles.choices, default=Roles.CUSTOMER)
    display_name = models.CharField(max_length=120, blank=True)
    avatar = models.URLField(blank=True)

    # Enhanced profile fields
    bio = models.TextField(max_length=500, blank=True)
    phone_regex = RegexValidator(regex=r'^\+?1?\d{9,15}$', message="Phone number must be entered in the format: '+999999999'. Up to 15 digits allowed.")
    phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True)
    date_of_birth = models.DateField(null=True, blank=True)

    # Address fields
    address_line_1 = models.CharField(max_length=255, blank=True)
    address_line_2 = models.CharField(max_length=255, blank=True)
    city = models.CharField(max_length=100, blank=True)
    state = models.CharField(max_length=100, blank=True)
    postal_code = models.CharField(max_length=20, blank=True)
    country = models.CharField(max_length=100, blank=True)

    # Verification and status
    is_verified = models.BooleanField(default=False)
    verification_status = models.CharField(max_length=20, choices=VerificationStatus.choices, default=VerificationStatus.PENDING)
    verification_document = models.URLField(blank=True, help_text="URL to verification document")

    # Business fields for sellers/freelancers
    business_name = models.CharField(max_length=200, blank=True)
    business_registration_number = models.CharField(max_length=100, blank=True)
    tax_id = models.CharField(max_length=50, blank=True)

    # Social media links
    website = models.URLField(blank=True)
    linkedin = models.URLField(blank=True)
    twitter = models.URLField(blank=True)
    instagram = models.URLField(blank=True)

    # Account settings
    is_active_seller = models.BooleanField(default=True)
    email_notifications = models.BooleanField(default=True)
    sms_notifications = models.BooleanField(default=False)

    # Timestamps
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    profile_completed_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return self.username

    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}".strip() or self.display_name or self.username

    @property
    def is_profile_complete(self):
        required_fields = [self.first_name, self.last_name, self.email, self.phone_number]
        if self.role in [self.Roles.SELLER, self.Roles.FREELANCER]:
            required_fields.extend([self.business_name, self.address_line_1, self.city, self.country])
        return all(field for field in required_fields)

    def mark_profile_complete(self):
        if self.is_profile_complete and not self.profile_completed_at:
            self.profile_completed_at = timezone.now()
            self.save(update_fields=['profile_completed_at'])


class UserSkill(models.Model):
    """Skills for freelancers"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='skills')
    name = models.CharField(max_length=100)
    level = models.CharField(max_length=20, choices=[
        ('beginner', 'Beginner'),
        ('intermediate', 'Intermediate'),
        ('advanced', 'Advanced'),
        ('expert', 'Expert')
    ], default='intermediate')
    years_experience = models.PositiveIntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ['user', 'name']

    def __str__(self):
        return f"{self.user.username} - {self.name} ({self.level})"


class UserEducation(models.Model):
    """Education history for freelancers"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='education')
    institution = models.CharField(max_length=200)
    degree = models.CharField(max_length=100)
    field_of_study = models.CharField(max_length=100, blank=True)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_current = models.BooleanField(default=False)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.user.username} - {self.degree} at {self.institution}"


class UserExperience(models.Model):
    """Work experience for freelancers"""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='experience')
    company = models.CharField(max_length=200)
    position = models.CharField(max_length=100)
    start_date = models.DateField()
    end_date = models.DateField(null=True, blank=True)
    is_current = models.BooleanField(default=False)
    description = models.TextField(blank=True)

    class Meta:
        ordering = ['-start_date']

    def __str__(self):
        return f"{self.user.username} - {self.position} at {self.company}"
