
from django.contrib.auth.models import AbstractUser
from django.db import models

class User(AbstractUser):
    class Roles(models.TextChoices):
        CUSTOMER = "customer","Customer"
        SELLER = "seller","Seller"
        FREELANCER = "freelancer","Freelancer"

    role = models.CharField(max_length=20, choices=Roles.choices, default=Roles.CUSTOMER)
    display_name = models.CharField(max_length=120, blank=True)
    avatar = models.URLField(blank=True)

    def __str__(self):
        return self.username
