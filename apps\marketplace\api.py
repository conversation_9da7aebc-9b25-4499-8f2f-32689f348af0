
from rest_framework import viewsets, permissions
from .models import Store, Product, Service, Category
from .serializers import StoreSerializer, ProductSerializer, ServiceSerializer, CategorySerializer

class IsOwnerOrReadOnly(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        return getattr(obj, "owner_id", None) == getattr(request.user, "id", None)

class StoreViewSet(viewsets.ModelViewSet):
    queryset = Store.objects.all()
    serializer_class = StoreSerializer
    permission_classes = [IsOwnerOrReadOnly]
    def perform_create(self, serializer):
        serializer.save(owner=self.request.user)

class ProductViewSet(viewsets.ModelViewSet):
    queryset = Product.objects.all().select_related("store","category")
    serializer_class = ProductSerializer

class ServiceViewSet(viewsets.ModelViewSet):
    queryset = Service.objects.all().select_related("store","category")
    serializer_class = ServiceSerializer

class CategoryViewSet(viewsets.ModelViewSet):
    queryset = Category.objects.all()
    serializer_class = CategorySerializer


from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Q
from rest_framework import status

class SearchViewSet(viewsets.ViewSet):
    @action(detail=False, methods=['get'])
    def search(self, request):
        q = request.query_params.get('q','').strip()
        if not q:
            return Response({'results': []})
        products = Product.objects.filter(Q(title__icontains=q) | Q(description__icontains=q))[:50]
        services = Service.objects.filter(Q(title__icontains=q) | Q(description__icontains=q))[:50]
        from .serializers import ProductSerializer, ServiceSerializer
        return Response({
            'products': ProductSerializer(products, many=True).data,
            'services': ServiceSerializer(services, many=True).data
        }, status=status.HTTP_200_OK)
