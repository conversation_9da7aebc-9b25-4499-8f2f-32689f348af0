from django.contrib import admin
from .models import Coupon, CouponUsage, FlashSale, AffiliateProgram, AffiliateReferral


@admin.register(Coupon)
class CouponAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'discount_type', 'discount_value', 'is_active', 'valid_from', 'valid_until']
    list_filter = ['discount_type', 'coupon_type', 'is_active', 'created_at']
    search_fields = ['code', 'name']
    readonly_fields = ['used_count']


@admin.register(CouponUsage)
class CouponUsageAdmin(admin.ModelAdmin):
    list_display = ['coupon', 'user', 'order', 'discount_amount', 'used_at']
    list_filter = ['used_at']
    search_fields = ['coupon__code', 'user__username']


@admin.register(FlashSale)
class FlashSaleAdmin(admin.ModelAdmin):
    list_display = ['name', 'discount_percentage', 'start_time', 'end_time', 'is_active']
    list_filter = ['is_active', 'start_time']
    search_fields = ['name']


@admin.register(AffiliateProgram)
class AffiliateProgramAdmin(admin.ModelAdmin):
    list_display = ['affiliate', 'referral_code', 'commission_rate', 'total_referrals', 'total_commission']
    search_fields = ['affiliate__username', 'referral_code']


@admin.register(AffiliateReferral)
class AffiliateReferralAdmin(admin.ModelAdmin):
    list_display = ['affiliate_program', 'referred_user', 'commission_amount', 'is_commission_paid', 'created_at']
    list_filter = ['is_commission_paid', 'created_at']
