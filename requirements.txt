# Core Django packages
Django>=4.2,<5.0
djangorestframework>=3.14,<3.16
djangorestframework-simplejwt>=5.2,<6.0

# Database
psycopg2-binary>=2.9,<3.0

# CORS handling
django-cors-headers>=4.0,<5.0

# Additional packages for enhanced functionality
Pillow>=10.0,<11.0  # Image processing
redis>=4.5,<5.0  # Caching and sessions
celery>=5.3,<6.0  # Background tasks
django-extensions>=3.2,<4.0  # Development utilities
python-decouple>=3.8,<4.0  # Environment variable management

# API documentation
drf-spectacular>=0.26,<1.0

# File storage (optional - for cloud storage)
django-storages>=1.14,<2.0
boto3>=1.28,<2.0

# Email backend (optional)
django-anymail>=10.0,<11.0

# Development and testing
pytest>=7.4,<8.0
pytest-django>=4.5,<5.0
factory-boy>=3.3,<4.0
coverage>=7.2,<8.0

# Security
django-ratelimit>=4.0,<5.0
django-guardian>=2.4,<3.0

# Search (optional - for advanced search)
# elasticsearch-dsl>=8.8,<9.0
# django-elasticsearch-dsl>=7.3,<8.0
